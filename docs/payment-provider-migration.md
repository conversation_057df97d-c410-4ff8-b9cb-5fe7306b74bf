# Payment Provider Migration Guide

## Overview

This document outlines the changes made to remove Airwallex and prepare the codebase for a new payment provider integration.

## Files Removed

The following Airwallex-specific files have been completely removed:

1. `lib/airwallex-client.ts` - Airwallex API client wrapper
2. `types/airwallex.d.ts` - Airwallex TypeScript declarations
3. `docs/airwallex.md` - Airwallex integration documentation
4. `docs/airwallex-pm.md` - Airwallex product management documentation
5. `docs/airwallex-pr-review.md` - Airwallex PR review documentation
6. `app/api/payment/webhook/route.ts` - Airwallex webhook handler

## Dependencies Removed

- `@airwallex/components-sdk` - Removed from package.json

## Files Modified with Placeholders

### 1. `components/messages/payment-dialog.tsx`

- Removed Airwallex SDK imports and initialization
- Added TODO comments for new payment provider integration
- Replaced CardElement type with generic `any` type
- Added placeholder logic for payment element creation and event handling

### 2. `app/enquiries/payment-actions.ts`

- Removed Airwallex client import and usage
- Added placeholder payment intent creation logic
- Added TODO comments for new payment provider client integration

### 3. `app/api/payment/webhook/route.ts`

- Updated webhook handling comments to be provider-agnostic
- Added TODO comments for new payment provider webhook signature verification
- Updated environment variable references

## Environment Variables to Update

### Remove these Airwallex-specific variables:

```bash
AIRWALLEX_API_KEY=
AIRWALLEX_CLIENT_ID=
AIRWALLEX_WEBHOOK_SECRET=
NEXT_PUBLIC_AIRWALLEX_ENV=
```

### Add these for new payment provider:

```bash
# TODO: Replace with actual environment variable names for new provider
NEW_PAYMENT_API_KEY=your_new_provider_api_key
NEW_PAYMENT_CLIENT_ID=your_new_provider_client_id
NEW_PAYMENT_WEBHOOK_SECRET=your_new_provider_webhook_secret
NEXT_PUBLIC_NEW_PAYMENT_ENV=demo  # or prod
```

## Database Schema (Preserved)

The following database components are kept as they are payment-provider agnostic:

- **Tables**: `quotes`, `provider_ledger`, `withdrawal_requests`, `webhook_events`
- **Functions**: `create_payment_intent_for_quote`, `submit_payment_for_quote`, `process_webhook_event`
- **Enums**: `quote_status`, `transaction_type`, `withdrawal_status`

These can be reused with any payment provider.

## Supabase RPC Functions for Payment Integration

The following RPC functions are available for payment processing and should be used by the new payment provider integration:

### 1. `create_payment_intent_for_quote`

**Purpose**: Securely updates a quote with payment intent details and changes status to `payment_intent_created`.

**Signature**:
```sql
create_payment_intent_for_quote(
  p_quote_id UUID,
  p_payment_intent_id TEXT
) RETURNS VOID
```

**Parameters**:
- `p_quote_id`: The UUID of the quote to update
- `p_payment_intent_id`: The payment intent ID from the payment provider

**Security**:
- Uses `SECURITY DEFINER` to bypass RLS
- Only allows clients to update their own quotes
- Only works if quote is in `pending` status
- Validates that the user is the quote's client

**Usage Example**:
```typescript
const { error } = await supabase.rpc('create_payment_intent_for_quote', {
  p_quote_id: quoteId,
  p_payment_intent_id: paymentIntent.id,
});
```

### 2. `submit_payment_for_quote`

**Purpose**: Updates quote status from `payment_intent_created` to `payment_processing` when user submits payment.

**Signature**:
```sql
submit_payment_for_quote(p_quote_id UUID) RETURNS VOID
```

**Parameters**:
- `p_quote_id`: The UUID of the quote to update

**Security**:
- Uses `SECURITY DEFINER` to bypass RLS
- Only allows clients to update their own quotes
- Only works if quote is in `payment_intent_created` status
- Validates that payment_intent_id is not null

**Usage Example**:
```typescript
const { error } = await supabase.rpc('submit_payment_for_quote', {
  p_quote_id: quoteId,
});
```

### 3. `process_webhook_event`

**Purpose**: Atomically checks and records webhook events to prevent duplicate processing (idempotency).

**Signature**:
```sql
process_webhook_event(
  p_event_id TEXT,
  p_event_type TEXT,
  p_payment_intent_id TEXT
) RETURNS BOOLEAN
```

**Parameters**:
- `p_event_id`: Unique event ID from payment provider webhook
- `p_event_type`: Type of webhook event (e.g., "payment_intent.succeeded")
- `p_payment_intent_id`: Payment intent ID associated with the event

**Returns**:
- `TRUE`: Event is new and was recorded (proceed with processing)
- `FALSE`: Event was already processed (skip processing)

**Security**:
- Uses `SECURITY DEFINER` to bypass RLS
- Only accessible by service role (webhooks)
- Uses `ON CONFLICT DO NOTHING` for atomic idempotency

**Usage Example**:
```typescript
const { data: isNewEvent, error } = await supabase.rpc('process_webhook_event', {
  p_event_id: eventId,
  p_event_type: event.name,
  p_payment_intent_id: paymentIntentId,
});

if (isNewEvent) {
  // Process the webhook event
} else {
  // Event already processed, skip
}
```

## Database Tables and Enums

### Payment-Related Tables

#### 1. `quotes` Table (Extended)
The quotes table has been extended with payment-related columns:

- `payment_intent_id TEXT` - Payment intent ID from payment provider
- `paid_at TIMESTAMPTZ` - Timestamp when payment was completed
- `provider_earnings DECIMAL(10,2)` - Amount provider will receive (after platform fee)
- `fee DECIMAL(10,2)` - Platform fee amount (20% of total)

#### 2. `provider_ledger` Table
Tracks all financial transactions for providers:

```sql
CREATE TABLE provider_ledger (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID NOT NULL REFERENCES profiles(id),
  transaction_type transaction_type NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL,
  quote_id UUID REFERENCES quotes(id),
  withdrawal_id UUID REFERENCES withdrawal_requests(id),
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

#### 3. `withdrawal_requests` Table
Manages provider withdrawal requests:

```sql
CREATE TABLE withdrawal_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID NOT NULL REFERENCES profiles(id),
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL,
  bank_details JSONB NOT NULL,
  status withdrawal_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  processed_at TIMESTAMPTZ
);
```

#### 4. `webhook_events` Table
Prevents duplicate webhook processing:

```sql
CREATE TABLE webhook_events (
  event_id TEXT PRIMARY KEY,
  event_type TEXT NOT NULL,
  payment_intent_id TEXT,
  processed_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Enums

#### 1. `quote_status` Enum
Extended with payment states:
- `pending` - Initial state
- `payment_intent_created` - Payment intent created, ready for payment
- `payment_processing` - Payment submitted, awaiting confirmation
- `payment_failed` - Payment failed
- `paid` - Payment successful
- `rejected`, `expired`, `cancelled`, `superceded` - Other states

#### 2. `transaction_type` Enum
For ledger entries:
- `payment` - Payment received from client
- `withdrawal` - Withdrawal to provider
- `fee` - Platform fee deduction
- `refund` - Refund to client

#### 3. `withdrawal_status` Enum
For withdrawal requests:
- `pending` - Awaiting processing
- `completed` - Successfully processed
- `rejected` - Rejected by admin

## Files Preserved

### `lib/payment-utils.ts`

This file contains generic payment calculation utilities (service fees, provider earnings) and is payment-provider agnostic. No changes needed.

## Payment Flow Using RPC Functions

The payment system follows this flow using the RPC functions:

### 1. Payment Intent Creation
```typescript
// In app/enquiries/payment-actions.ts
export async function createPaymentIntent(quoteId: string) {
  // 1. Create payment intent with new payment provider
  const paymentIntent = await newPaymentProvider.createPaymentIntent({
    amount: quote.price,
    currency: quote.currency,
    metadata: { quote_id: quoteId }
  });

  // 2. Update quote with payment intent details
  await supabase.rpc('create_payment_intent_for_quote', {
    p_quote_id: quoteId,
    p_payment_intent_id: paymentIntent.id,
  });

  return {
    id: paymentIntent.id,
    client_secret: paymentIntent.client_secret,
  };
}
```

### 2. Payment Submission
```typescript
// In app/enquiries/payment-actions.ts
export async function submitPayment(quoteId: string) {
  // Update quote status to payment_processing
  await supabase.rpc('submit_payment_for_quote', {
    p_quote_id: quoteId,
  });
}
```

### 3. Webhook Processing
```typescript
// In app/api/payment/webhook/route.ts
export async function POST(request: NextRequest) {
  const event = await request.json();

  // Check idempotency
  const { data: isNewEvent } = await supabase.rpc('process_webhook_event', {
    p_event_id: event.id,
    p_event_type: event.type,
    p_payment_intent_id: event.data.id,
  });

  if (!isNewEvent) {
    return NextResponse.json({ status: 'already_processed' });
  }

  // Process payment success/failure
  if (event.type === 'payment_intent.succeeded') {
    await handlePaymentSuccess(event.data);
  }
}
```

### 4. Payment Success Handling
The webhook handler directly updates the database (bypassing RLS with service role):
- Updates quote status to `paid`
- Sets `paid_at` timestamp
- Calculates and stores `provider_earnings` and `fee`
- Creates ledger entries for payment and fee

## Next Steps for New Payment Provider Integration

1. **Choose Payment Provider**: Select and set up account with new payment provider
2. **Create Client Library**: Implement `lib/new-payment-client.ts` similar to the removed Airwallex client
3. **Update Type Definitions**: Create TypeScript definitions for new provider's SDK
4. **Update Payment Dialog**: Replace placeholder code in `payment-dialog.tsx` with actual integration
5. **Update Payment Actions**: Replace placeholder code in `payment-actions.ts` with actual API calls
6. **Update Webhook Handler**: Replace placeholder code in webhook route with actual signature verification
7. **Update Environment Variables**: Set up new provider's API keys and configuration
8. **Test Integration**: Thoroughly test payment flow with new provider
9. **Update Documentation**: Create new integration documentation

## Webhook Implementation Notes

The existing webhook handler in `app/api/payment/webhook/route.ts` provides a complete structure that can be adapted for any payment provider:

### Key Features:
1. **Signature Verification**: Prevents unauthorized webhook calls
2. **Idempotency**: Prevents duplicate processing using `process_webhook_event` RPC
3. **Atomic Operations**: All database updates are atomic to prevent inconsistencies
4. **Error Handling**: Comprehensive error handling and logging

### What to Update for New Provider:
1. **Signature Verification Method**: Update `verifyWebhookSignature` function
2. **Event Names**: Update event type matching (e.g., `payment_intent.succeeded`)
3. **Event Structure**: Update how you extract payment intent data from webhook payload
4. **Environment Variables**: Update webhook secret environment variable name

### Payment Success Handler:
The `handlePaymentSuccess` function performs these operations:
- Validates payment intent status
- Fetches quote in `payment_processing` state
- Calculates service fee and provider earnings using `lib/payment-utils.ts`
- Updates quote to `paid` status with earnings breakdown
- Creates ledger entries for payment and fee transactions
- All operations are atomic within a database transaction

### Payment Failure Handler:
The `handlePaymentFailure` function:
- Updates quote status back to `payment_failed`
- Allows user to retry payment
- Logs failure details for debugging

## Testing Checklist

Before deploying with new payment provider:

- [ ] Payment intent creation works
- [ ] Payment form renders correctly
- [ ] Payment confirmation works
- [ ] Webhook signature verification works
- [ ] Payment success updates quote status correctly
- [ ] Payment failure handling works
- [ ] Provider earnings are calculated correctly
- [ ] Ledger entries are created properly
- [ ] Error handling works as expected

## Security and RLS Policies

### Row Level Security (RLS)
The payment system uses RLS policies to ensure data security:

#### Quotes Table Policies:
- **SELECT**: Users can view quotes where they are either the client or the provider
- **UPDATE**: Clients can update their own quotes, but only in specific states
- **Payment Fields**: Protected fields (`payment_intent_id`, `provider_earnings`, `fee`, `paid_at`) can only be updated via RPC functions

#### Webhook Events Table:
- **No RLS Policies**: Only accessible by service role (webhooks bypass RLS)
- Regular users cannot access webhook events table

#### Provider Ledger Table:
- **SELECT**: Providers can only view their own ledger entries
- **INSERT/UPDATE**: Only via service role (webhooks and admin functions)

### Security Considerations for New Provider:
1. **Webhook Endpoint Security**: Always verify webhook signatures in production
2. **Environment Variables**: Store API keys and secrets securely
3. **Payment Intent Validation**: Always validate payment amounts and currency
4. **Idempotency**: Use `process_webhook_event` RPC to prevent duplicate processing
5. **Atomic Operations**: Use database transactions for financial operations
6. **Audit Logging**: All payment operations are logged in the ledger table

## Notes

- All database migrations and functions remain intact
- The payment flow logic structure is preserved
- Only provider-specific code has been removed/replaced with placeholders
- The 20% service fee calculation remains unchanged
- RPC functions provide secure, atomic operations for payment processing
- Webhook idempotency is built-in and provider-agnostic
