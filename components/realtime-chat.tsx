"use client";

import type { KeyboardEvent } from "react";
import { useEffect, useOptimistic, useRef, useTransition } from "react";
import { toast } from "sonner";
import { Temporal } from "temporal-polyfill";

import { revalidatePathAction } from "@/app/actions";
import {
  createQuote,
  markRoomAsRead,
  storeChatMessage,
} from "@/app/enquiries/actions";
import { ChatMessageItem } from "@/components/chat-message";
import {
  ChatInputArea,
  type ChatInputAreaHandle,
} from "@/components/messages/chat-input-area";
import {
  type QuoteFormClientInput,
  QuoteFormClientSchema,
} from "@/components/messages/quote-form";
import { useRealtimeTableChanges } from "@/hooks/use-realtime-table-changes";
import { convertLocalToUTCDateTime } from "@/lib/quote-utils";
import { type ChatMessage } from "@/types/chat";
import type { QuoteWithWorkshopDetails } from "@/types/quote";

interface RealtimeChatProps {
  roomId: string;
  workshopId: string;
  workshopName: string;
  workshopCurrency: string;
  organizationName: string;
  currentUserId: string;
  messages: ChatMessage[];
  isClient: boolean;
  initialMessage?: string;
}

/**
 * Displays a real-time chat interface for a workshop room, allowing users to send messages and create or duplicate quotes.
 *
 * Provides optimistic UI updates for sent messages, subscribes to real-time message changes, and integrates with a chat input area for message and quote handling.
 *
 * @param roomId - The unique identifier for the chat room.
 * @param workshopId - The unique identifier for the workshop.
 * @param workshopName - The name of the workshop.
 * @param workshopCurrency - The currency code used in the workshop.
 * @param organizationName - The name of the organization.
 * @param currentUserId - The identifier of the current user.
 * @param messages - The initial list of chat messages.
 * @param isClient - Indicates if the current user is a client.
 * @param initialMessage - (Optional) The initial message to prefill in the input area.
 */
export function RealtimeChat({
  roomId,
  workshopId,
  workshopName,
  workshopCurrency,
  organizationName,
  currentUserId,
  messages,
  isClient,
  initialMessage = "",
}: RealtimeChatProps) {
  // Form management - uncontrolled
  const messageRef = useRef<HTMLTextAreaElement>(null);
  const chatInputAreaRef = useRef<ChatInputAreaHandle>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isPending, startTransition] = useTransition();

  // Optimistic UI for immediate message display
  const [optimisticMessages, addOptimisticMessage] = useOptimistic(
    messages,
    (state, newMessage: ChatMessage) => [...state, newMessage],
  );

  // Quote creation handler
  const handleCreateQuote = async (quoteData: QuoteFormClientInput) => {
    // Handle quote creation when sent
    const result = QuoteFormClientSchema.safeParse(quoteData);
    if (!result.success) {
      console.error("Invalid quote data format", result.error);
      toast.error("Invalid quote data");
      return { success: false, error: "Invalid quote data" };
    }

    try {
      const utcDatetime = convertLocalToUTCDateTime(quoteData.proposedDatetime);

      const { success, error } = await createQuote({
        workshopId: quoteData.workshopId,
        chatRoomId: roomId,
        proposedDatetime: utcDatetime,
        location: quoteData.location,
        price: quoteData.price,
        currency: quoteData.currency,
        notes: quoteData.notes || "",
      });

      if (!success) {
        toast.error(error || "Failed to create quote");
      }

      return { success, error };
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to create quote";
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Set initial message when initialMessage prop changes
  useEffect(() => {
    if (initialMessage && messageRef.current) {
      messageRef.current.value = initialMessage;
    }
  }, [initialMessage]);

  // Mark room as read when component mounts
  useEffect(() => {
    markRoomAsRead(roomId);
  }, [roomId]);

  const scrollToBottom = () => {
    if (scrollContainerRef.current) {
      // Set top to 0 to scroll to bottom because flex-col-reverse
      scrollContainerRef.current.scrollTop = 0;
    }
  };

  // Handle duplicating a quote with smart date adjustment
  const handleDuplicateQuote = (quote: QuoteWithWorkshopDetails) => {
    chatInputAreaRef.current?.duplicateQuote(quote);
    queueMicrotask(() => scrollToBottom());
  };

  // Subscribe to realtime changes
  const { isConnected } = useRealtimeTableChanges({
    table: "chat_messages",
    filter: {
      column: "room_id",
      operator: "eq",
      value: roomId,
    },
    onTableChange: (_payload) => {
      revalidatePathAction(`/enquiries/${roomId}`);
    },
  });

  // Send a message
  const sendMessage = () => {
    const messageValue = messageRef.current?.value.trim() || "";
    // Only allow sending with a message
    if (!messageValue || !isConnected || isPending) return;

    const message = messageValue;
    if (messageRef.current) {
      messageRef.current.value = "";
    }

    startTransition(async () => {
      addOptimisticMessage({
        id: `optimistic-${Date.now()}`,
        content: message,
        user: {
          id: currentUserId,
          name: "You", // This will be replaced by the actual name from the server
        },
        createdAt: Temporal.Now.instant().toString(),
        roomId: roomId,
      });

      const result = await storeChatMessage(roomId, message);
      if (!result.message) {
        toast.error("Failed to send message");
      }
    });
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (
      (e.key === "Enter" && e.shiftKey) ||
      (e.key === "Enter" && e.metaKey) ||
      (e.key === "Enter" && e.ctrlKey)
    ) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage();
  };

  return (
    <div className="relative flex flex-1 flex-col bg-background text-foreground antialiased">
      <div
        ref={scrollContainerRef}
        className="absolute inset-0 flex flex-col-reverse overflow-y-auto"
      >
        {/* Input area - visually at the bottom of reversed container  */}
        <div className="w-full max-w-2xl">
          <ChatInputArea
            ref={chatInputAreaRef}
            messageRef={messageRef}
            onSendMessage={handleSendMessage}
            onKeyDown={handleKeyDown}
            createQuote={handleCreateQuote}
            workshopId={workshopId}
            workshopName={workshopName}
            workshopCurrency={workshopCurrency}
            organizationName={organizationName}
            isClient={isClient}
            isConnected={isConnected}
            isPending={isPending}
          />
        </div>

        <div className="flex max-w-2xl flex-col p-4 pb-0">
          {optimisticMessages.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground">
              No messages yet. Start the conversation!
            </div>
          ) : (
            optimisticMessages.map((message) => {
              const isOwnMessage = message.user?.id === currentUserId;
              const isOptimistic = message.id.startsWith("optimistic-");

              return (
                <ChatMessageItem
                  key={message.id}
                  message={message}
                  isOwnMessage={isOwnMessage}
                  isClient={isClient}
                  isOptimistic={isOptimistic}
                  onDuplicateQuote={handleDuplicateQuote}
                />
              );
            })
          )}
        </div>
      </div>
    </div>
  );
}
