import Image from "next/image";
import { Suspense } from "react";

import { NavbarClient } from "@/components/navbar-client";
import { getUser } from "@/lib/auth";
import { cn } from "@/lib/utils";
import { createClient } from "@/utils/supabase/server";

export type NavbarVariant = "default" | "landing-client" | "providers";

interface NavbarProps {
  className?: string;
  showAuth?: boolean;
  variant?: NavbarVariant;
}

// Non-async component that doesn't depend on user data
export function Navbar({
  className,
  showAuth = true,
  variant = "default",
}: NavbarProps) {
  return (
    <header className={cn("w-full bg-background", className)}>
      <div className="container flex h-16 items-center">
        {/* Navigation and Auth section - wrapped in Suspense */}
        <Suspense fallback={<NavbarLogo href="/" />}>
          <UserDependentNavbar showAuth={showAuth} variant={variant} />
        </Suspense>
      </div>
    </header>
  );
}

// Component for the logo section
function NavbarLogo({ href }: { href: string }) {
  return (
    <a href={href} className="flex space-x-2">
      <Image
        src="/pulse-space.png"
        alt="Pulse Space"
        width={196}
        height={40}
        priority
      />
    </a>
  );
}

// Async component that fetches and depends on user data
async function UserDependentNavbar({
  showAuth,
  variant,
}: {
  showAuth: boolean;
  variant: NavbarVariant;
}) {
  // Fetch user data on the server
  const user = await getUser();

  // Fetch user profile and organization data if user is logged in
  let organization = null;
  let userType: "provider" | "client" | "admin" | null = null;

  if (user) {
    const supabase = await createClient();

    // Get user profile to determine user type
    const { data: profile } = await supabase
      .from("profiles")
      .select("user_type")
      .eq("id", user.id)
      .single();

    if (profile) {
      userType = profile.user_type;

      // If provider, fetch organization data
      if (userType === "provider") {
        const { data: provider } = await supabase
          .from("providers")
          .select("organization_id")
          .eq("id", user.id)
          .single();

        if (provider?.organization_id) {
          const { data: orgData } = await supabase
            .from("provider_organizations")
            .select("profile_photo_url, name")
            .eq("id", provider.organization_id)
            .single();

          organization = orgData
            ? {
                profile_photo_url: orgData.profile_photo_url ?? undefined,
                name: orgData.name,
              }
            : null;
        }
      }
    }
  }

  // Determine logo href based on user type
  const logoHref = userType === "provider" ? "/providers" : "/";

  // Return the logo and NavbarClient
  return (
    <>
      <NavbarLogo href={logoHref} />
      <NavbarClient
        user={user}
        organization={organization}
        className=""
        showAuth={showAuth}
        userType={userType}
        variant={variant}
      />
    </>
  );
}
