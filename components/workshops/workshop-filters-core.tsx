"use client";

import { Tag } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import type { WorkshopFormat } from "@/types/types";

interface WorkshopFiltersCoreProps {
  initialFilters: {
    type: WorkshopFormat[];
    category: string[];
    page?: number;
  };
  categories: { id: string; name: string }[];
  mode: "desktop" | "mobile";
  onReset?: () => void; // Optional callback for mobile to handle collapse
}

export function WorkshopFiltersCore({
  initialFilters,
  categories,
  mode,
  onReset,
}: WorkshopFiltersCoreProps) {
  const router = useRouter();

  // Local state for form control, initialized from server-provided values
  const [selectedTypes, setSelectedTypes] = useState<WorkshopFormat[]>(
    initialFilters.type,
  );

  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    initialFilters.category,
  );

  const applyFilters = useCallback(() => {
    // Create a new URLSearchParams object
    const params = new URLSearchParams();

    // Add selected workshop types
    if (selectedTypes.length > 0) {
      selectedTypes.forEach((type) => {
        params.append("type", type);
      });
    }

    // Add selected categories
    if (selectedCategories.length > 0) {
      selectedCategories.forEach((cat) => {
        params.append("category", cat);
      });
    }

    // Maintain current page if it exists
    if (initialFilters.page && initialFilters.page > 1) {
      params.set("page", initialFilters.page.toString());
    }

    // Navigate to the new URL
    router.push(`/workshops?${params.toString()}`);
  }, [router, selectedTypes, selectedCategories, initialFilters.page]);

  const resetFilters = useCallback(() => {
    setSelectedTypes([]);
    setSelectedCategories([]);
    router.push("/workshops");
    // Call mobile's onReset if provided (to collapse mobile filter)
    onReset?.();
  }, [router, onReset]);

  const toggleCategory = useCallback((catId: string) => {
    setSelectedCategories((current) => {
      if (current.includes(catId)) {
        return current.filter((c) => c !== catId);
      } else {
        return [...current, catId];
      }
    });
  }, []);

  const toggleType = useCallback((type: WorkshopFormat) => {
    setSelectedTypes((current) => {
      if (current.includes(type)) {
        return current.filter((t) => t !== type);
      } else {
        return [...current, type];
      }
    });
  }, []);

  // Type-safe workshop formats
  const workshopTypes = [
    { id: "in_person", label: "In Person" },
    { id: "online", label: "Online" },
    { id: "hybrid", label: "Hybrid" },
  ] as const;

  if (mode === "desktop") {
    return (
      <div className="w-64 space-y-4">
        <Card>
          <CardHeader className="p-4">
            <CardTitle className="flex items-center text-lg">
              <Tag className="mr-2 h-4 w-4" />
              Category
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 pt-0">
            {Array.from(new Set(categories.map((cat) => cat.id))).map(
              (catId) => {
                const cat = categories.find((c) => c.id === catId);
                if (!cat) return null;

                return (
                  <div key={cat.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`desktop-category-${cat.id}`}
                      checked={selectedCategories.includes(cat.id)}
                      onCheckedChange={() => toggleCategory(cat.id)}
                    />
                    <Label
                      htmlFor={`desktop-category-${cat.id}`}
                      className="text-sm font-normal"
                    >
                      {cat.name}
                    </Label>
                  </div>
                );
              },
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="p-4">
            <CardTitle className="flex items-center text-lg">
              <Tag className="mr-2 h-4 w-4" />
              Workshop Type
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 pt-0">
            {workshopTypes.map((workshopType) => (
              <div
                key={workshopType.id}
                className="flex items-center space-x-2"
              >
                <Checkbox
                  id={`desktop-type-${workshopType.id}`}
                  checked={selectedTypes.includes(workshopType.id)}
                  onCheckedChange={() => toggleType(workshopType.id)}
                />
                <Label
                  htmlFor={`desktop-type-${workshopType.id}`}
                  className="text-sm font-normal"
                >
                  {workshopType.label}
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>

        <div className="flex flex-col space-y-2">
          <Button onClick={applyFilters}>Apply Filters</Button>
          <Button
            onClick={resetFilters}
            variant="outline"
            className="border-dashed"
          >
            Reset Filters
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <section>
        <h3 className="mb-3 text-base font-medium">Categories</h3>
        <div className="space-y-2">
          {Array.from(new Set(categories.map((cat) => cat.id))).map((catId) => {
            const cat = categories.find((c) => c.id === catId);
            if (!cat) return null;

            return (
              <div key={cat.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`mobile-category-${cat.id}`}
                  checked={selectedCategories.includes(cat.id)}
                  onCheckedChange={() => toggleCategory(cat.id)}
                />
                <Label
                  htmlFor={`mobile-category-${cat.id}`}
                  className="cursor-pointer text-sm font-normal"
                >
                  {cat.name}
                </Label>
              </div>
            );
          })}
        </div>
      </section>

      <section>
        <h3 className="mb-3 text-base font-medium">Workshop Type</h3>
        <div className="space-y-2">
          {workshopTypes.map((workshopType) => (
            <div key={workshopType.id} className="flex items-center space-x-2">
              <Checkbox
                id={`mobile-type-${workshopType.id}`}
                checked={selectedTypes.includes(workshopType.id)}
                onCheckedChange={() => toggleType(workshopType.id)}
              />
              <Label
                htmlFor={`mobile-type-${workshopType.id}`}
                className="cursor-pointer text-sm font-normal"
              >
                {workshopType.label}
              </Label>
            </div>
          ))}
        </div>
      </section>

      <div className="flex flex-col space-y-2 pt-2">
        <Button onClick={applyFilters}>Apply Filters</Button>
        <Button
          onClick={resetFilters}
          variant="outline"
          className="border-dashed"
        >
          Reset Filters
        </Button>
      </div>
    </div>
  );
}
