"use client";

import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";

export function BackButton() {
  const router = useRouter();
  const [canGoBack, setCanGoBack] = useState(false);

  useEffect(() => {
    // Check if the referrer is from the same domain
    if (typeof window !== "undefined" && document.referrer) {
      try {
        const referrerUrl = new URL(document.referrer);
        const currentUrl = new URL(window.location.href);
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

        // Check if referrer is from the same domain
        if (baseUrl) {
          const baseDomain = new URL(baseUrl).hostname;
          setCanGoBack(
            referrerUrl.hostname === baseDomain ||
              referrerUrl.hostname === currentUrl.hostname,
          );
        } else {
          // Fallback to comparing hostnames directly
          setCanGoBack(referrerUrl.hostname === currentUrl.hostname);
        }
      } catch {
        // If parsing fails, default to not going back
        setCanGoBack(false);
      }
    }
  }, []);

  const handleClick = () => {
    if (canGoBack) {
      router.back();
    } else {
      router.push("/workshops");
    }
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={handleClick}
      className="h-8 w-8"
    >
      <ChevronLeft className="h-4 w-4" />
    </Button>
  );
}
