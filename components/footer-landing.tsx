import Image from "next/image";
import Link from "next/link";

import { cn } from "@/lib/utils";

interface FooterProps {
  className?: string;
}

export function Footer({ className }: FooterProps) {
  return (
    <footer
      className={cn(
        "bg-[#111827] px-4 py-16 text-white sm:px-6 lg:px-8",
        className,
      )}
    >
      <div className="mx-auto max-w-7xl">
        <div className="grid gap-8 sm:grid-cols-4">
          {/* Company Info */}
          <div className="sm:col-span-2">
            <h3 className="mb-4 text-2xl font-bold">Pulse Space</h3>
            <p className="mb-6 text-gray-300">
              The leading place for corporate health and wellness events.
              Helping HR teams create engaged, healthy, and happy workplaces.
            </p>
            <div className="flex items-center gap-2">
              <Image
                src="/landing-client/email-icon.svg"
                alt=""
                width={20}
                height={20}
                className="h-5 w-5"
              />
              <a
                href="mailto:<EMAIL>"
                className="text-gray-300 hover:text-white"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Resources */}
          <div>
            <h4 className="mb-4 text-lg font-semibold">Resources</h4>
            <ul className="space-y-3">
              {/*<li>*/}
              {/*  <Link href="#" className="text-gray-300 hover:text-white">*/}
              {/*    Contact us*/}
              {/*  </Link>*/}
              {/*</li>*/}
              <li>
                <Link
                  href="/providers"
                  className="text-gray-300 hover:text-white"
                >
                  Become a provider
                </Link>
              </li>
              {/*<li>*/}
              {/*  <Link href="#" className="text-gray-300 hover:text-white">*/}
              {/*    FAQs*/}
              {/*  </Link>*/}
              {/*</li>*/}
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="mb-4 text-lg font-semibold">Legal</h4>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/privacy.html"
                  target="_blank"
                  className="text-gray-300 hover:text-white"
                >
                  Privacy policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms.html"
                  target="_blank"
                  className="text-gray-300 hover:text-white"
                >
                  Terms of use
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-12 border-t border-gray-800 pt-6 text-center">
          <p className="text-gray-400">© 2025 Pulse Space</p>
        </div>
      </div>
    </footer>
  );
}
