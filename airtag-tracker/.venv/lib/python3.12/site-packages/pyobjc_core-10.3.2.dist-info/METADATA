Metadata-Version: 2.1
Name: pyobjc-core
Version: 10.3.2
Summary: Python<->ObjC Interoperability Module
Home-page: https://github.com/ronaldoussoren/pyobjc
Author: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, many others stretching back through the reaches of time...
Author-email: <PERSON><PERSON><PERSON><PERSON><PERSON>@mac.com
Maintainer: <PERSON>
Maintainer-email: <PERSON><PERSON><PERSON><PERSON><PERSON>@mac.com
License: MIT License
Keywords: Objective-C,Cocoa
Platform: MacOS X
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X :: Cocoa
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Objective C
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.8
Description-Content-Type: text/x-rst; charset=UTF-8
Project-URL: Documentation, https://pyobjc.readthedocs.io/en/latest/
Project-URL: Issue tracker, https://github.com/ronaldoussoren/pyobjc/issues
Project-URL: Repository, https://github.com/ronaldoussoren/pyobjc

PyObjC is a bridge between Python and Objective-C.  It allows full
featured Cocoa applications to be written in pure Python.  It is also
easy to use other frameworks containing Objective-C class libraries
from Python and to mix in Objective-C, C and C++ source.

Python is a highly dynamic programming language with a shallow learning
curve.  It combines remarkable power with very clear syntax.

PyObjC also supports full introspection of Objective-C classes and
direct invocation of Objective-C APIs from the interactive interpreter.

Project links
-------------

* `Documentation <https://pyobjc.readthedocs.io/en/latest/>`_

* `Issue Tracker <https://github.com/ronaldoussoren/pyobjc/issues>`_

* `Repository <https://github.com/ronaldoussoren/pyobjc/>`_

