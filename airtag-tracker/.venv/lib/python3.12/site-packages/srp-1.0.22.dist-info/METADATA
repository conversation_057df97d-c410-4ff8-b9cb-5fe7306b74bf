Metadata-Version: 2.1
Name: srp
Version: 1.0.22
Summary: Secure Remote Password
Home-page: https://github.com/cocagne/pysrp
Download-URL: http://pypi.python.org/pypi/srp
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Platform: OS Independent
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python
Classifier: Topic :: Security
Provides: srp
License-File: LICENSE
Requires-Dist: six



This package provides an implementation of the Secure Remote Password
protocol (SRP). SRP is a cryptographically strong authentication
protocol for password-based, mutual authentication over an insecure
network connection.

Unlike other common challenge-response autentication protocols, such
as Kerberos and SSL, SRP does not rely on an external infrastructure
of trusted key servers or certificate management. Instead, SRP server
applications use verification keys derived from each user's password
to determine the authenticity of a network connection.

SRP provides mutual-authentication in that successful authentication
requires both sides of the connection to have knowledge of the
user's password. If the client side lacks the user's password or the
server side lacks the proper verification key, the authentication will
fail.

Unlike SSL, SRP does not directly encrypt all data flowing through
the authenticated connection. However, successful authentication does
result in a cryptographically strong shared key that can be used
for symmetric-key encryption.

For a full description of the pysrp package and the SRP protocol,
please refer to the `srp module documentation`_.

.. _`srp module documentation`: http://packages.python.org/srp

