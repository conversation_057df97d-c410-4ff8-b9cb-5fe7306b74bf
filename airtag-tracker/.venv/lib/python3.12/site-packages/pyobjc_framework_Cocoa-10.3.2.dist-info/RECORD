AppKit/_AppKit.cpython-312-darwin.so,sha256=i2kwI2CHarCqUhhDZv6tEEKYEQ37tsPq6rwu8Rj3_Is,503792
AppKit/__init__.py,sha256=AEUp8WL1c8flnbRBlgN5RGC4j5Zi1n53Xn_ZI6jZjxs,7637
AppKit/_inlines.cpython-312-darwin.so,sha256=gcAitV3y8QTtdMix1ValY8hEa_MiRwDtx4nNW14kGl0,67352
AppKit/_metadata.py,sha256=SEGSPzkMryNgHvsLShG1SnAJWL1Ov4IzDLvEDtv5ws0,808845
AppKit/_nsapp.py,sha256=1ho5WDDxnv0F8JApxlsjGNAiUNXAQbCJKgdibnPuPdk,663
Cocoa/__init__.py,sha256=55Dh8Y-JKIIadnSJpA--v8p-krzhTnL3wMXpdLD-SVM,614
CoreFoundation/_CoreFoundation.cpython-312-darwin.so,sha256=tZ6NB1AxD163LloL4qc7K5WEEJ_syyIr3W2sAbNBrdY,168184
CoreFoundation/__init__.py,sha256=4MH13d7vmaVrx7Up3o7I29nCbg_tkcLiHVUjvPHk_O0,966
CoreFoundation/_inlines.cpython-312-darwin.so,sha256=QBd-kA6WtR2sOmBAADLkSrGlpDqbeY6HFIGt0Li0xaw,88368
CoreFoundation/_metadata.py,sha256=gPWGzRhc71XDIijBexAiQ4CWmMozIlon1mFgA_Wo-8c,153234
CoreFoundation/_static.py,sha256=VBPABa1hz5MpMiFS6NTWmWp6jvjBSZB3pVZbylF-P4A,2880
Foundation/_Foundation.cpython-312-darwin.so,sha256=ifX-Ct1-8ov73EdZhBmJxBGR0AN-kekZHrMINlHg5lY,174568
Foundation/__init__.py,sha256=a3pzFYYwhjamCdne4w1Kp1yadKrltmn2p0wVb0j4sNo,6136
Foundation/_context.py,sha256=F-OTiqB1Cb2Wf5xWaFScgNOf8USRDKr_VKzJpcJdiNo,713
Foundation/_functiondefines.py,sha256=n9lLKjNOdZ2Gb7eKYr0V7TqnzPunyE64Y52n7qzDIME,1506
Foundation/_inlines.cpython-312-darwin.so,sha256=Rv8VUMyWGFvnvecKKmZ7vxveY6qEWfJMxKB4lP7ScZw,90848
Foundation/_metadata.py,sha256=_AIhgY4KRa9Sej8oTtbaqwYO384ZHEZFAdviADLdQpw,469645
Foundation/_nsindexset.py,sha256=2-EWXurn2BMYfywMJLDWwxQHFC21RKg96wHB01y_kNg,394
Foundation/_nsobject.py,sha256=_JgHmbU3z9wfnsB2ph5uUj4Vd0VgfCyn1zvt47ABobQ,8403
Foundation/_nsurl.py,sha256=sCAj6ZUzwYrbqhJ4254roa62XgpQjrz2P6j_JVixJOY,603
PyObjCTools/AppCategories.py,sha256=JobYSNPicQXCVyqmoVAQevWGgOmqYrU5IbJ4m65DKM4,841
PyObjCTools/AppHelper.py,sha256=xT4_ewfGsquIP2ETyM2mt_R37DdbTgjYSBnAhhZ8gzo,9605
PyObjCTools/Conversion.py,sha256=CGIIghG1oE4ptsTh2Fivn8OmVne8n3YaYseSU-MSPzc,7913
PyObjCTools/FndCategories.py,sha256=sMFkfNyP6jxI8Rjc7ZnZGHgDAN5-rGPNRgZ0UTlWYgQ,1027
pyobjc_framework_Cocoa-10.3.2.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
pyobjc_framework_Cocoa-10.3.2.dist-info/LICENSE.txt,sha256=VBYOCJp5HziM90a14Txl68gt3y2rIJpcoZAoVkfX4Ho,1249
pyobjc_framework_Cocoa-10.3.2.dist-info/METADATA,sha256=FOzJXwDyR1VmNlqFOiHYrW5y_-6tGxbDetOrp9jZ4QQ,2266
pyobjc_framework_Cocoa-10.3.2.dist-info/RECORD,,
pyobjc_framework_Cocoa-10.3.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyobjc_framework_Cocoa-10.3.2.dist-info/WHEEL,sha256=w9Wib2Cggr6a0jsmQwU18YkLmVsyOqV25G3M7UiDLV0,115
pyobjc_framework_Cocoa-10.3.2.dist-info/top_level.txt,sha256=1QsnXKsqfT9IZo77EJz5mxcnXKL_5uurtMmFxy_zu8k,62
