import crypto from "crypto";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

import { calculatePaymentBreakdown } from "@/lib/payment-utils";
import { createClient } from "@/utils/supabase/server";

/**
 * TODO: Handles incoming payment webhook events via HTTP POST.
 *
 * Replace with new payment provider's webhook handling:
 * - Verify webhook signature using new provider's method
 * - Check event idempotency
 * - Process payment success and failure events
 * - Return appropriate HTTP responses
 *
 * @returns A JSON response indicating the result of webhook processing.
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();

    // TODO: Verify webhook signature for security using new payment provider
    const signature = request.headers.get("x-webhook-signature"); // Update header name for new provider
    const webhookSecret = process.env.NEW_PAYMENT_WEBHOOK_SECRET; // Update env var name

    if (webhookSecret && signature) {
      // TODO: Replace with new payment provider's signature verification
      const isValid = verifyWebhookSignature(body, signature, webhookSecret);
      if (!isValid) {
        console.error("Invalid webhook signature");
        return NextResponse.json(
          { error: "Invalid signature" },
          { status: 401 },
        );
      }
    } else if (process.env.NODE_ENV === "production") {
      // In production, webhook secret is required
      console.error("Missing webhook signature or secret in production");
      return NextResponse.json({ error: "Missing signature" }, { status: 401 });
    } else {
      // In development, log warning but allow through
      console.warn("Webhook signature verification skipped in development");
    }

    const event = JSON.parse(body);

    // Idempotency check - use dedicated webhook events table
    const eventId = event.id;
    const paymentIntentId = event.data?.object?.id;

    if (!eventId || !paymentIntentId) {
      console.error("Missing event ID or payment intent ID");
      return NextResponse.json(
        { error: "Invalid webhook event" },
        { status: 400 },
      );
    }

    const supabase = await createClient();

    // Check if this event has already been processed atomically
    const { data: isNewEvent, error: idempotencyError } = await supabase.rpc(
      "process_webhook_event",
      {
        p_event_id: eventId,
        p_event_type: event.name,
        p_payment_intent_id: paymentIntentId,
      },
    );

    if (idempotencyError) {
      console.error("Idempotency check error:", idempotencyError);
      // Continue processing to be safe - better to process twice than not at all
    } else if (!isNewEvent) {
      console.log(`Event ${eventId} already processed - skipping`);
      return NextResponse.json({
        received: true,
        status: "already_processed",
      });
    }

    // TODO: Handle different webhook events for new payment provider
    // Update event names to match new provider's webhook events
    switch (event.name || event.type) {
      case "payment_intent.succeeded": // Update event name for new provider
        await handlePaymentSuccess(event.data.object || event.data);
        break;

      case "payment_intent.payment_failed": // Update event name for new provider
        await handlePaymentFailure(event.data.object || event.data);
        break;

      default:
        console.log(`Unhandled webhook event: ${event.name || event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook error:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 },
    );
  }
}

/**
 * TODO: Verify webhook signature using new payment provider's method
 * @param payload Raw webhook payload as string
 * @param signature Signature from webhook header
 * @param secret Webhook secret from new payment provider
 * @returns true if signature is valid
 */
function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string,
): boolean {
  if (!signature || !secret) {
    return false;
  }

  try {
    // TODO: Replace with new payment provider's signature verification method
    // Example for HMAC SHA256 (update algorithm if different):
    // Remove any prefix if present (update prefix for new provider)
    const cleanSignature = signature.replace(/^sha256=/, "");

    const expectedSignature = crypto
      .createHmac("sha256", secret) // Update algorithm if needed
      .update(payload, "utf8")
      .digest("hex");

    return crypto.timingSafeEqual(
      Buffer.from(cleanSignature, "hex"),
      Buffer.from(expectedSignature, "hex"),
    );
  } catch (error) {
    console.error("Webhook signature verification error:", error);
    return false;
  }
}

/**
 * Processes a successful payment intent by updating the associated quote to "paid" status and recording payment details.
 *
 * If the quote is not found or not in the expected state, or if the payment intent is not marked as "SUCCEEDED", the function logs an error and takes no further action.
 *
 * @param paymentIntent - The payment intent object containing payment and metadata information.
 */
async function handlePaymentSuccess(paymentIntent: {
  id: string;
  amount: number;
  currency: string;
  captured_amount?: number;
  status?:
    | "REQUIRES_PAYMENT_METHOD"
    | "REQUIRES_CUSTOMER_ACTION"
    | "REQUIRES_CAPTURE"
    | "PENDING"
    | "SUCCEEDED"
    | "CANCELLED";
  metadata?: { quote_id?: string };
}) {
  const supabase = await createClient();

  const quoteId = paymentIntent.metadata?.quote_id;
  if (!quoteId) {
    console.error("No quote ID in payment intent metadata");
    return;
  }

  if (paymentIntent.status && paymentIntent.status !== "SUCCEEDED") {
    console.error(
      `Payment intent ${paymentIntent.id} has unexpected status: ${paymentIntent.status}`,
    );
    return;
  }

  // Log payment details for debugging
  console.log("Payment details:", {
    id: paymentIntent.id,
    amount: paymentIntent.amount,
    captured_amount: paymentIntent.captured_amount,
    status: paymentIntent.status,
    currency: paymentIntent.currency,
  });

  try {
    // Fetch the quote and validate it's in the correct state for payment success
    const { data: quote, error: quoteError } = await supabase
      .from("quotes")
      .select("*")
      .eq("id", quoteId)
      .eq("status", "payment_processing") // Only process if in payment_processing state
      .single();

    if (quoteError || !quote) {
      console.error(
        "Quote not found or not in payment_processing state:",
        quoteId,
      );
      return;
    }

    const amount = paymentIntent.amount;
    const { serviceFee: fee, providerEarnings } =
      calculatePaymentBreakdown(amount);

    // Update quote directly - webhook uses service role which bypasses RLS
    const { error: updateError } = await supabase
      .from("quotes")
      .update({
        status: "paid",
        paid_at: new Date().toISOString(),
        payment_intent_id: paymentIntent.id,
        provider_earnings: providerEarnings,
        fee: fee,
      })
      .eq("id", quoteId)
      .eq("status", "payment_processing"); // Double-check status hasn't changed

    if (updateError) {
      console.error("Failed to update quote status:", updateError);
      return;
    }

    // TODO: Add ledger entries atomically
    // const { error: ledgerError } = await supabase
    //   .from("provider_ledger")
    //   .insert([
    //     {
    //       provider_id: quote.provider_id, // Need to get this from quote join
    //       transaction_type: "payment",
    //       amount: quote.price * 0.8,
    //       currency: quote.currency,
    //       quote_id: quoteId,
    //       description: `Payment received for quote ${quoteId}`,
    //     },
    //     {
    //       provider_id: quote.provider_id,
    //       transaction_type: "fee",
    //       amount: -(quote.price * 0.2), // Negative for fee deduction
    //       currency: quote.currency,
    //       quote_id: quoteId,
    //       description: `Platform fee for quote ${quoteId}`,
    //     },
    //   ]);

    console.log(`Payment successful for quote ${quoteId}`);
  } catch (error) {
    console.error("Error processing payment success:", error);

    // TODO: In production, implement retry logic or dead letter queue
    // for failed webhook processing
  }
}

async function handlePaymentFailure(paymentIntent: {
  metadata?: { quote_id?: string };
}) {
  const supabase = await createClient();

  const quoteId = paymentIntent.metadata?.quote_id;
  if (!quoteId) {
    console.error("No quote ID in payment intent metadata");
    return;
  }

  try {
    // Update quote status to payment_failed, but only if currently in payment_processing
    const { error: updateError } = await supabase
      .from("quotes")
      .update({
        status: "payment_failed",
      })
      .eq("id", quoteId)
      .eq("status", "payment_processing"); // Only update if in payment_processing state

    if (updateError) {
      console.error("Failed to update quote status:", updateError);
      return;
    }

    console.log(
      `Payment failed for quote ${quoteId}, status set to payment_failed`,
    );
  } catch (error) {
    console.error("Error processing payment failure:", error);
  }
}
