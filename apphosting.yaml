# Settings for Backend (on Cloud Run).
# See https://firebase.google.com/docs/app-hosting/configure#cloud-run
runConfig:
  minInstances: 0
  # maxInstances: 100
  # concurrency: 80
  # cpu: 1
  # memoryMiB: 512

env:
  - variable: NEXT_PUBLIC_SUPABASE_URL
    value: https://qsfshjpttsgvxjvjyjgh.supabase.co
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_SUPABASE_ANON_KEY
    value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFzZnNoanB0dHNndnhqdmp5amdoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE1MjcxMTMsImV4cCI6MjA1NzEwMzExM30.aDbtiolYCNBvIq9IOgpSWwoqXEBQu57q0cwxcGZP7mg
    availability:
      - BUILD
      - RUNTIME
  - variable: NEXT_PUBLIC_BASE_URL
    value: https://pulsespace.co
    availability:
      - BUILD
      - RUNTIME
  - variable: EMAIL_HOST
    value: smtp.forwardemail.net
    availability:
      - BUILD
      - RUNTIME
  - variable: EMAIL_PORT
    value: 465
    availability:
      - BUILD
      - RUNTIME

  # Grant access to secrets in Cloud Secret Manager.
  # See https://firebase.google.com/docs/app-hosting/configure#secret-parameters
  - variable: SUPABASE_SERVICE_ROLE_KEY
    secret: supabaseServiceRoleKey

  - variable: UPLOADTHING_TOKEN
    secret: uploadthingToken

  - variable: EMAIL_USERNAME
    secret: emailUsername

  - variable: EMAIL_PASSWORD
    secret: emailPassword
