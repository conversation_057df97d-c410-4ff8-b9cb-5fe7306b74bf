{
  "$schema": "node_modules/wrangler/config-schema.json",
  "main": ".open-next/worker.js",
  "name": "pulse-space",
  "compatibility_date": "2024-12-30",
  "compatibility_flags": ["nodejs_compat"],
  "assets": {
    "directory": ".open-next/assets",
    "binding": "ASSETS"
  },
  "services": [
    {
      "binding": "WORKER_SELF_REFERENCE",
      // The service should match the "name" of your worker
      "service": "pulse-space"
    }
  ],
  "kv_namespaces": [
    // Create a KV binding with the binding name "NEXT_INC_CACHE_KV"
    // to enable the KV based caching:
    // {
    //   "binding": "NEXT_INC_CACHE_KV",
    //   "id": "<BINDING_ID>"
    // }
  ]
}
