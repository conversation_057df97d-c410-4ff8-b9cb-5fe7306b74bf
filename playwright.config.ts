// playwright.config.ts
import { defineConfig, devices } from "@playwright/test";
import path from "path";

// Use process.env.PORT by default and fallback to 3000 if not specified.
const PORT = process.env.PORT || 3000;
const baseURL = `http://localhost:${PORT}`;

export default defineConfig({
  timeout: 90 * 1000, // 90 seconds
  retries: process.env.CI ? 2 : 0, // Retries in CI
  workers: process.env.CI ? 1 : undefined, // Limit workers in CI

  // Test directory
  testDir: path.join(__dirname, "tests/e2e"),

  // Define projects for major browsers
  projects: [
    // Setup project runs before all tests
    {
      name: "setup",
      testMatch: /global\.setup\.ts/,
      teardown: "teardown",
    },
    // Test projects with dependencies on setup
    {
      name: "Desktop Chrome",
      use: { ...devices["Desktop Chrome"] },
      dependencies: ["setup"],
    },
    {
      name: "Mobile Safari",
      use: { ...devices["iPhone 13"] },
      dependencies: ["setup"],
    },
    // Teardown project runs after all tests
    {
      name: "teardown",
      testMatch: /global\.teardown\.ts/,
    },
  ],

  use: {
    // Base URL to use in actions like `await page.goto('/')`
    baseURL: baseURL,

    // Collect trace when retrying the failed test
    trace: "on-first-retry",

    // Record video for failed tests
    video: "retain-on-failure",
  },

  // Reporter to use. See https://playwright.dev/docs/test-reporters
  reporter: process.env.CI ? "github" : "list",

  // Run your local dev server before starting the tests:
  // https://playwright.dev/docs/test-advanced#launching-a-development-web-server-during-the-tests
  webServer: {
    command: process.env.CI ? "pnpm run start" : "pnpm run dev",
    url: baseURL,
    timeout: 120 * 1000, // 120 seconds
    reuseExistingServer: !process.env.CI,
    // stdout: 'pipe',
    // stderr: 'pipe',
  },
});
